using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Services;

namespace ProManage.Forms.ReusableForms
{
    /// <summary>
    /// Role entry form for creating new roles
    /// </summary>
    public partial class RoleEntryForm : XtraForm
    {
        #region Properties

        /// <summary>
        /// Gets the entered role name
        /// </summary>
        public string RoleName { get; private set; }

        #endregion

        #region Constructor

        public RoleEntryForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initialize form properties and setup
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Set form properties
                this.Text = "Create New Role";
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.StartPosition = FormStartPosition.CenterParent;
                this.Size = new System.Drawing.Size(400, 200);

                // Setup event handlers
                SetupEventHandlers();

                // Focus on role name field
                txtRoleName.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing Role Entry Form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Setup event handlers for form controls
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Button events
                btnSave.Click += BtnSave_Click;
                btnCancel.Click += BtnCancel_Click;

                // Text field events
                txtRoleName.KeyPress += TxtRoleName_KeyPress;
                txtRoleName.TextChanged += TxtRoleName_TextChanged;

                // Form events
                this.Load += RoleEntryForm_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting up event handlers: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle form load event
        /// </summary>
        private void RoleEntryForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Clear any existing text
                txtRoleName.Text = string.Empty;
                
                // Update button states
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle role name text changed event
        /// </summary>
        private void TxtRoleName_TextChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        /// <summary>
        /// Handle key press in role name field
        /// </summary>
        private void TxtRoleName_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Allow Enter key to trigger save
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                if (btnSave.Enabled)
                {
                    BtnSave_Click(sender, e);
                }
            }
            // Allow Escape key to cancel
            else if (e.KeyChar == (char)Keys.Escape)
            {
                e.Handled = true;
                BtnCancel_Click(sender, e);
            }
        }

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (!ValidateInput())
                    return;

                var roleName = txtRoleName.Text.Trim();

                // Check if role already exists
                if (PermissionService.RoleExists(roleName))
                {
                    MessageBox.Show($"A role with the name '{roleName}' already exists. Please choose a different name.",
                        "Role Already Exists", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRoleName.Focus();
                    txtRoleName.SelectAll();
                    return;
                }

                // Create the role
                var roleId = PermissionService.CreateRole(roleName);
                if (roleId > 0)
                {
                    RoleName = roleName;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to create role. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Validate user input
        /// </summary>
        private bool ValidateInput()
        {
            var roleName = txtRoleName.Text.Trim();

            if (string.IsNullOrEmpty(roleName))
            {
                MessageBox.Show("Please enter a role name.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                return false;
            }

            if (roleName.Length < 2)
            {
                MessageBox.Show("Role name must be at least 2 characters long.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                txtRoleName.SelectAll();
                return false;
            }

            if (roleName.Length > 50)
            {
                MessageBox.Show("Role name cannot exceed 50 characters.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                txtRoleName.SelectAll();
                return false;
            }

            // Check for invalid characters
            if (roleName.Contains("|") || roleName.Contains(",") || roleName.Contains(";"))
            {
                MessageBox.Show("Role name cannot contain special characters like |, ,, or ;", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                txtRoleName.SelectAll();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Update button states based on form state
        /// </summary>
        private void UpdateButtonStates()
        {
            var hasText = !string.IsNullOrWhiteSpace(txtRoleName.Text);
            btnSave.Enabled = hasText;
        }

        #endregion
    }
}
